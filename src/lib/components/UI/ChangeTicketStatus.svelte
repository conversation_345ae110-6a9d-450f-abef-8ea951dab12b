<script lang="ts">
	import { t, language } from '$lib/stores/i18n';
	import { get } from 'svelte/store';

	import { triggerRefresh } from '$lib/stores/refreshStore';

	import { enhance } from '$app/forms';
	import { Button, Modal } from 'flowbite-svelte';
	import { InfoCircleSolid, CheckOutline } from 'flowbite-svelte-icons';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';

	export let ticket: any;
	export let statuses: [
		{ id: 1; name: 'default' },
		{ id: 2; name: 'open' },
		{ id: 3; name: 'assigned' },
		{ id: 4; name: 'waiting' },
		{ id: 5; name: 'pending_to_close' },
		{ id: 6; name: 'closed' }
	];
	export let ticket_topics: any[];
	export let isDropDownItem: boolean = false;
	export let showButton: boolean = true; // New prop to control button visibility
	export let modalOpen: boolean = false; // Bindable prop to control modal state externally
	export let onSuccess: () => void = () => {}; // Callback for successful operations

	// Get unique case_type and case_topic lists
	const caseTypes = [...new Set(ticket_topics.map((item) => item.case_type))];

	let changeTicketStatusForm: HTMLFormElement;
	let changeTicketStatusModalOpen = false;
	let changingTicketStatus: any = null;
	let selectedTicketStatusId: number = 0;
	let selectedTicketCaseType: string = '';
	let selectedTicketCaseTopic: string[] = [];
	let statusChanged: boolean = false;
	let initialStatusId: number = 0;

	// Sync external modalOpen prop with internal state
	$: if (modalOpen && !changeTicketStatusModalOpen && ticket) {
		openChangeTicketStatusModal(ticket);
	}

	// State variables for handling messages - REMOVED, using toast instead
	// let showSuccessMessage = false;
	// let showErrorMessage = false;
	// let successMessage = '';
	// let errorMessage = '';

	function openChangeTicketStatusModal(ticket: any) {
		changingTicketStatus = { ...ticket };
		initialStatusId = ticket.status.id ? ticket.status.id : ticket.status_id ? ticket.status_id : 0;
		selectedTicketStatusId = initialStatusId; // Use initialStatusId instead of ticket.status.id
		changeTicketStatusModalOpen = true;
		statusChanged = false;
		// Reset case type and topic selections
		selectedTicketCaseType = '';
		selectedTicketCaseTopic = [];
		// Reset messages when opening modal - REMOVED, using toast instead
		// showSuccessMessage = false;
		// showErrorMessage = false;
		// successMessage = '';
		// errorMessage = '';
		hidden3 = false;
	}

	function handleChangeTicketStatusSubmit(event: Event) {
		// Reset messages - REMOVED, using toast instead
		// showSuccessMessage = false;
		// showErrorMessage = false;
		// successMessage = '';
		// errorMessage = '';

		// Check if status has been changed
		if (selectedTicketStatusId === initialStatusId) {
			event.preventDefault();
			// Use toast for error message
			import('$lib/stores/toastStore').then(({ toastStore }) => {
				toastStore.add('Please select a different status or cancel.', 'error');
			});
			return;
		}
	}

	$: enhanceOptions = {
		modalOpen: changeTicketStatusModalOpen,
		setModalOpen: (value: boolean) => {
			changeTicketStatusModalOpen = value;
			modalOpen = value; // Sync back to external prop
		},
		useToastOnSuccess: true,
		useToastOnError: true,
		closeModalOnSuccess: true,
		successMessage: t('ticket_status_success'),
		onSuccess: () => {
			if (onSuccess) onSuccess();
			triggerRefresh();
		}
	};

	// Reactive statement to filter case topics based on selected case type
	$: filteredCaseTopics = ticket_topics
		.filter((topic) => topic.case_type === selectedTicketCaseType)
		.map((topic) => ({ value: topic.case_topic, name: topic.case_topic }));

	// Reset selectedCaseTopic when selectedCaseType changes
	$: if (selectedTicketCaseType !== '') {
		selectedTicketCaseTopic = []; // Reset case topic array
	}

	// Track when status changes
	$: {
		if (selectedTicketStatusId !== initialStatusId && initialStatusId !== 0) {
			statusChanged = true;
		} else {
			statusChanged = false;
		}
	}

	// Check if both case type and case topic are selected
	$: isSelectionValid = selectedTicketCaseType !== '' && selectedTicketCaseTopic.length > 0;

	// Check if form is valid for submission
	// Check if we're trying to close a ticket that's not already closed
	$: isClosingTicket = selectedTicketStatusId === 6 && initialStatusId !== 6;

	// Check if form is valid for submission
	$: isFormValid = (() => {
		if (!statusChanged) return false;

		// If closing a ticket (changing to closed), require case type and topic selection
		if (isClosingTicket) {
			return isSelectionValid;
		}

		// For all other status changes (including changing from closed to assigned), allow it
		return true;
	})();

	let hidden3 = true;

	// Function to get status color classes
	function getStatusColorClasses(statusId: number) {
		switch (statusId) {
			case 2: // 'open'
				return 'border-green-200 bg-green-100 text-green-700';
			case 3: // 'assigned'
				return 'border-blue-200 bg-blue-100 text-blue-700';
			case 4: // 'waiting'
				return 'border-yellow-200 bg-yellow-200 text-yellow-700';
			case 5: // 'pending to close'
				return 'border-gray-200 bg-gray-200 text-gray-700';
			case 6: // 'closed'
				return 'border-gray-200 bg-gray-200 text-gray-700';
			default:
				return 'border-gray-200 bg-gray-100 text-gray-700';
		}
	}

	// Get status name by ID
	function getStatusNameById(statusId: number) {
		const status = statuses.find((s) => s.id === statusId);
		// return status ? status.name.charAt(0).toUpperCase() + status.name.slice(1) : 'Unknown';
		return status
			// ? status.name.charAt(0).toUpperCase() + status.name.slice(1).split('_').join(' ')
			? t("tickets_" + status.name)
			: 'Unknown';
	}

	// Update the getTicketTopicId function to handle the array of case topics
	function getTicketTopicId(caseType: string, caseTopics: string[]) {
		return ticket_topics
			.filter((topic) => caseTopics.includes(topic.case_topic) && topic.case_type === caseType)
			.map((topic) => topic.id);
	}

	// Function to determine which statuses should be displayed based on current status
	function getAvailableStatuses(currentStatusId: number) {
		const baseStatuses = statuses.filter((status) => status.name !== 'default');

		if (currentStatusId === 6) {
			// If current status is 'closed'
			// Only show 'assigned' status, exclude 'open', 'waiting', 'pending_to_close', and 'closed'
			return baseStatuses.filter((status) => status.name === 'assigned');
		} else {
			// For other statuses, exclude 'waiting', 'pending_to_close', and 'assigned'
			return baseStatuses.filter(
				(status) =>
					status.name !== 'waiting' &&
					status.name !== 'pending_to_close' &&
					status.name !== 'assigned'
			);
		}
	}

	// Get available statuses based on current status
	$: availableStatuses = getAvailableStatuses(initialStatusId);
	$: currentLanguage = get(language);

	// Add this reactive statement to debug
	$: {
		console.log('selectedTicketStatusId:', selectedTicketStatusId, typeof selectedTicketStatusId);
		console.log('initialStatusId:', initialStatusId, typeof initialStatusId);
		console.log('isClosingTicket:', isClosingTicket);
		console.log('availableStatuses:', availableStatuses);
		console.log('statusChanged:', statusChanged);
		console.log('isFormValid:', isFormValid);
		console.log('changingTicketStatus:', changingTicketStatus);
		console.log('Current Language:', currentLanguage);
	}
</script>

{#if showButton}
	{#if isDropDownItem}
		<Button
			id="change-ticket-status-dropdown-button"
			color="none"
			class="w-full justify-start text-left hover:bg-gray-100"
			on:click={() => openChangeTicketStatusModal(ticket)}
		>
			{t('change_status')}
		</Button>
	{:else}
		<Button
			id="change-ticket-status-button"
			size="xs"
			class="bg-gray-900 text-white"
			color="dark"
			on:click={() => openChangeTicketStatusModal(ticket)}
		>
			{t('status')}
		</Button>
	{/if}
{/if}

<Modal
	id="change-ticket-status-modal"
	bind:open={changeTicketStatusModalOpen}
	size="md"
	autoclose={false}
	class="w-full"
>
	<h2 slot="header" class="inline-flex items-center gap-4">
		<div class="inline-flex items-center">
			<InfoCircleSolid class="me-2.5 h-5 w-5" />{t('ticket_status')}
		</div>
		<span
			class={`inline-flex rounded-full px-3 py-1 text-sm font-medium ${getStatusColorClasses(initialStatusId)}`}
		>
			{getStatusNameById(initialStatusId)}
		</span>
	</h2>

	{#if changingTicketStatus}
		<form
			id="change-ticket-status-form"
			bind:this={changeTicketStatusForm}
			action="?/change_ticket_status"
			method="POST"
			use:enhance={() => handleEnhance(enhanceOptions)}
			on:submit={handleChangeTicketStatusSubmit}
		>
			<input
				id="change-ticket-status-ticket-id"
				type="hidden"
				name="ticket_id"
				value={changingTicketStatus.id}
			/>
			<div>
				<!-- Status Selection with Radio Buttons -->
				<label id="change-ticket-status-label" class="mb-3 block text-sm font-medium text-gray-700">
					{t('select_new_ticket_status')}
				</label>
				<div id="change-ticket-status-options" class="grid grid-cols-1 gap-3 md:grid-cols-3">
					{#each availableStatuses.sort((a, b) => {
						// Make "closed" (id: 6) appear last
						if (a.id === 6) return 1;
						if (b.id === 6) return -1;
						return a.id - b.id;
					}) as status}
						{@const isCurrentStatus = status.id === initialStatusId}
						<label
							id="change-ticket-status-option-{status.id}"
							class="flex w-full items-center rounded-md border p-4 hover:cursor-pointer {getStatusColorClasses(status.id)} {isCurrentStatus ? 'opacity-50' : ''}"
						>
							<input
								id="change-ticket-status-radio-{status.id}"
								type="radio"
								name="ticketStatus"
								value={status.id}
								bind:group={selectedTicketStatusId}
								class="mr-3 h-4 w-4 border-gray-300 text-gray-700 focus:ring-2 focus:ring-gray-700 disabled:mr-0 disabled:h-0 disabled:w-0"
								disabled={isCurrentStatus}
							/>
							<div class="flex-1 text-gray-700">
								{t("tickets_" + status.name)}
								{#if isCurrentStatus}
									<span class="ml-1">({t('current')})</span>
								{/if}
							</div>
						</label>
					{/each}
				</div>

				<!-- Case Type and Topic sections (only show when changing status to closed) -->
				{#if isClosingTicket}
					<div
						id="change-ticket-status-additional-info"
						class="mb-4 mt-4 rounded-md border border-red-100 bg-red-50 p-3"
					>
						<p class="text-sm text-red-800">
							{t('additional_info_required')}
						</p>
					</div>

					<!-- Case Type section with radio buttons -->
					<label
						id="change-ticket-status-case-type-label"
						class="mb-3 block text-sm font-medium text-gray-700"
					>
						{t('select_case_type')}
					</label>
					<div
						id="change-ticket-status-case-type-options"
						class="mb-4 grid grid-cols-2 gap-3 md:grid-cols-3"
					>
						{#each caseTypes as caseType}
							<label
								id="change-ticket-status-case-type-{caseType.replace(/\s+/g, '-').toLowerCase()}"
								class="flex w-full items-center rounded-md border border-gray-200 p-3 hover:cursor-pointer"
							>
								<input
									id="change-ticket-status-case-type-radio-{caseType.replace(/\s+/g, '-').toLowerCase()}"
									type="radio"
									name="caseType"
									value={caseType}
									bind:group={selectedTicketCaseType}
									class="mr-3 h-4 w-4 border-gray-300 text-gray-700 focus:ring-2 focus:ring-gray-700"
								/>
								<div class="flex-1 text-gray-700">
									{#if currentLanguage === 'th'}
										<div class="flex flex-col">
											<span>{t(caseType.toLowerCase().replace(/\s+/g, '_'))}</span>
											<span class="text-xs text-gray-500">({caseType})</span>
										</div>
									{:else}
										{caseType}
									{/if}
								</div>
							</label>
						{/each}
					</div>

					<!-- Case Topic section with multi-selectable boxes -->
					<label
						id="change-ticket-status-case-topic-label"
						class="mb-3 block text-sm font-medium text-gray-700"
					>
						{t('case_topic_multiple')}
					</label>
					<div
						id="change-ticket-status-case-topic-options"
						class="grid grid-cols-2 gap-3 md:grid-cols-2"
					>
						{#each filteredCaseTopics as caseTopic}
							{@const isSelected = selectedTicketCaseTopic.includes(caseTopic.value)}
							<button
								id="change-ticket-status-case-topic-{caseTopic.value
									.replace(/\s+/g, '-')
									.toLowerCase()}"
								type="button"
								class="rounded-md border p-3 text-left text-sm {isSelected
									? 'border-blue-500 bg-blue-100 text-gray-700 font-semibold'
									: 'border-gray-200'}"
								on:click={() => {
									if (isSelected) {
										// Remove from selection if already selected
										selectedTicketCaseTopic = selectedTicketCaseTopic.filter(
											(topic) => topic !== caseTopic.value
										);
									} else {
										// Add to selection
										selectedTicketCaseTopic = [...selectedTicketCaseTopic, caseTopic.value];
									}
								}}
							>
								{#if currentLanguage === 'th'}
									<div class="flex flex-col">
										<span>{t(caseTopic.name.toLowerCase().replace(/[\s\/-]+/g, '_'))}</span>
										<span class="text-xs text-gray-500">({caseTopic.name})</span>
									</div>
								{:else}
									{caseTopic.name}
								{/if}
							</button>
						{/each}
					</div>

					{#if selectedTicketCaseType !== '' && selectedTicketCaseTopic.length === 0}
						<p class="mt-2 text-sm text-red-600">
							{t('please_select_case_topic')}
						</p>
					{/if}
				{/if}

				<input
					id="change-ticket-status-new-status-id"
					type="hidden"
					name="new_status_id"
					value={selectedTicketStatusId}
				/>
				<input
					id="change-ticket-status-new-ticket-topic"
					type="hidden"
					name="new_ticket_topic"
					value={selectedTicketCaseType && selectedTicketCaseTopic.length > 0
						? getTicketTopicId(selectedTicketCaseType, selectedTicketCaseTopic).join(',')
						: ''}
				/>

				<!-- Debug information -->
				<!-- {#if selectedTicketStatusId}
					<div class="mt-2 text-xs text-gray-500">
						Debug: Selected Status ID = {selectedTicketStatusId}, Type = {typeof selectedTicketStatusId}
					</div>
				{/if} -->
			</div>
		</form>
	{/if}

	<!-- Confirm and Cancel Button -->
	<svelte:fragment slot="footer">
		<div class="flex items-center gap-2">
			<Button
				id="change-ticket-status-confirm-button"
				color="green"
				on:click={() => changeTicketStatusForm.requestSubmit()}
				disabled={!isFormValid}
				class={!isFormValid ? 'cursor-not-allowed opacity-50' : ''}
			>
				<CheckOutline class="mr-2 h-4 w-4" />
				{t('save')}
			</Button>
			<Button
				id="change-ticket-status-cancel-button"
				color="light"
				on:click={() => {
					changeTicketStatusModalOpen = false;
					modalOpen = false;
				}}>{t('cancel')}</Button
			>
		</div>
	</svelte:fragment>
</Modal>
