<script lang="ts">
	import { t } from '$lib/stores/i18n';
	import { triggerRefresh } from '$lib/stores/refreshStore';

	import { enhance } from '$app/forms';
	import { Button, Modal, Alert, Input, Label } from 'flowbite-svelte';
	import { InfoCircleSolid, SearchOutline, CheckOutline } from 'flowbite-svelte-icons';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';

	export let ticket: any;
	export let users: any[] = [];
	export let loggedInUsername: string = '';
	export let loggedInRole: string = '';
	export let isDropDownItem: boolean = false;
	export let showButton: boolean = true; // New prop to control button visibility
	export let onSuccess: () => void = () => {}; // Callback for successful operations

	$: filteredUsers = users.filter((user) => {
		if (ticket.owner.name === 'System' && loggedInRole === 'Agent') {
			return user.username === loggedInUsername;
		} else if (
			ticket.owner.name === 'System' &&
			(loggedInRole === 'Supervisor' || loggedInRole === 'Admin')
		) {
			return user.roles !== 'System';
		} else if (loggedInRole === 'Supervisor' || loggedInRole === 'Admin') {
			// return user.username !== ticket.owner.username && user.roles !== 'System';
			return user.roles !== 'System';
		} else {
			return false;
		}
	});

	let ticketTransferOwnerForm: HTMLFormElement;
	let ticketTransferOwnerModalOpen = false;
	let transferingTicketOwner: any = null;
	let selectedUserId: string | number = '';
	let searchQuery = '';

	// Filter users based on search query and sort by status
	$: searchResults = filteredUsers
		.filter((user) => {
			if (!searchQuery) return true;
			const lowerQuery = searchQuery.toLowerCase();
			return (
				user.name?.toLowerCase().includes(lowerQuery) ||
				user.username?.toLowerCase().includes(lowerQuery) ||
				user.first_name?.toLowerCase().includes(lowerQuery) ||
				user.last_name?.toLowerCase().includes(lowerQuery)
			);
		})
		.sort((a, b) => {
			// Sort by status: online first, then away, then offline
			const statusOrder = { online: 1, away: 2, offline: 3 };
			const statusA = statusOrder[a.status?.toLowerCase()] || 3; // Default to offline (3) if status is undefined
			const statusB = statusOrder[b.status?.toLowerCase()] || 3;
			return statusA - statusB;
		});

	function openTicketTransferOwnerModal(ticket: any) {
		transferingTicketOwner = { ...ticket };
		ticketTransferOwnerModalOpen = true;
		searchQuery = '';
		selectedUserId = '';
	}

	function handleTicketTransferOwnerSubmit(event: Event) {
		// Reset messages - REMOVED, using toast instead
	}

	function selectUser(userId: string | number) {
		selectedUserId = userId;
	}

	$: enhanceOptions = {
		modalOpen: ticketTransferOwnerModalOpen,
		setModalOpen: (value: boolean) => (ticketTransferOwnerModalOpen = value),
		useToastOnSuccess: true,
		useToastOnError: true,
		closeModalOnSuccess: true,
		onSuccess: () => {
			// Call the original onSuccess if provided
			if (onSuccess) onSuccess();
			// Trigger global refresh
			triggerRefresh();
		}
	};

	// Function to get user initials for the avatar
	function getUserInitials(name: string): string {
		if (!name) return '';
		const parts = name.split(' ');
		if (parts.length >= 2) {
			return (parts[0][0] + parts[1][0]).toUpperCase();
		}
		return name[0]?.toUpperCase() || '';
	}

	// Function to get color for user avatar based on status
	function getAvatarColor(user: any): string {
		if (!user.status) return 'bg-gray-400'; // Default gray for unknown status

		switch (user.status.toLowerCase()) {
			case 'online':
				return 'bg-green-400';
			case 'away':
				return 'bg-yellow-400';
			case 'offline':
			default:
				return 'bg-gray-400';
		}
	}

	// Function to get status indicator class
	function getStatusIndicator(status: string): string {
		if (!status) return 'hidden';

		switch (status.toLowerCase()) {
			case 'online':
				return 'bg-green-400';
			case 'away':
				return 'bg-yellow-400';
			case 'offline':
			default:
				return 'hidden'; // Hide indicator for offline users
		}
	}

	// Function to check if a user is the current ticket owner
	function isCurrentOwner(userId: string | number): boolean {
		return (
			transferingTicketOwner &&
			transferingTicketOwner.owner &&
			userId.toString() === transferingTicketOwner.owner.id.toString()
		);
	}
</script>

{#if showButton}
	{#if isDropDownItem}
		<Button
			id="transfer-ticket-owner-dropdown-button"
			color="none"
			class="w-full justify-start text-left hover:bg-gray-100"
			on:click={() => openTicketTransferOwnerModal(ticket)}
		>
			{t('transfer_ticket_ownership')}
		</Button>
	{:else}
		<Button
			id="transfer-ticket-owner-button"
			size="xs"
			class="bg-gray-900 text-white"
			color="dark"
			on:click={() => openTicketTransferOwnerModal(ticket)}
		>
			{t('transfer_ticket')}
		</Button>
	{/if}
{/if}

<Modal
	id="transfer-ticket-owner-modal"
	bind:open={ticketTransferOwnerModalOpen}
	size="md"
	autoclose={false}
	class="w-full"
>
	<!-- Header -->
	<h2 slot="header" class="inline-flex items-center">
		<InfoCircleSolid class="me-2.5 h-5 w-5" />{t('transfer_ticket_title')}
	</h2>

	{#if transferingTicketOwner}
		<!-- Display current owner information -->
		<div id="transfer-ticket-owner-current-owner" class="mb-4 rounded-lg bg-gray-100 p-3 shadow-md">
			<p class="font-medium text-gray-700">{t('current_owner')}</p>
			<div class="mt-2 flex items-center">
				<div
					class="mr-3 h-8 w-8 {getAvatarColor(
						transferingTicketOwner.owner
					)} flex items-center justify-center rounded-full"
				>
					<span class="text-white">{getUserInitials(transferingTicketOwner.owner.name)}</span>
				</div>
				<div>
					<p>{transferingTicketOwner.owner.name}</p>
					{#if transferingTicketOwner.owner.roles}
						<p class="text-xs text-gray-500">
							({transferingTicketOwner.owner.roles.map((role) => role.name).join(', ')})
						</p>
					{/if}
				</div>
			</div>
		</div>

		<form
			id="transfer-ticket-owner-form"
			bind:this={ticketTransferOwnerForm}
			action="?/ticket_transfer_owner"
			method="POST"
			use:enhance={() => handleEnhance(enhanceOptions)}
			on:submit={handleTicketTransferOwnerSubmit}
		>
			<input
				id="transfer-ticket-owner-ticket-id"
				type="hidden"
				name="ticket_id"
				value={transferingTicketOwner.id}
			/>
			<div class="mb-4">
				<Label
					id="transfer-ticket-owner-label"
					for="transfer-ticket-owner-search-input"
					class="mb-2 block font-semibold text-gray-700"
				>
					{t('select_new_owner')}
				</Label>

				<!-- Search input -->
				<div id="transfer-ticket-owner-search-container" class="relative mb-4">
					<div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
						<SearchOutline class="h-5 w-5 text-gray-500" />
					</div>
					<Input
						id="transfer-ticket-owner-search-input"
						placeholder={t('search')}
						class="pl-10 focus:border-blue-700 focus:ring-blue-700"
						bind:value={searchQuery}
					/>
				</div>

				<!-- User list -->
				<div
					id="transfer-ticket-owner-user-list"
					class="max-h-64 overflow-y-auto rounded-lg bg-gray-100 shadow-md"
				>
					{#if searchResults.length === 0}
						<div
							id="transfer-ticket-owner-no-results"
							class="flex items-center border-b p-3 text-gray-500"
						>
							<span>No assignee found</span>
						</div>
					{:else}
						{#each searchResults as user}
							{@const isCurrent = isCurrentOwner(user.id)}
							{@const isOffline = !user.status || user.status.toLowerCase() === 'offline'}
							{@const isAway = user.status && user.status.toLowerCase() === 'away'}
							{@const isSelected = selectedUserId === user.id}
							<div
								id="transfer-ticket-owner-user-{user.id}"
								class="flex items-center p-3 {isCurrent
									? 'cursor-not-allowed bg-gray-50 opacity-50'
									: 'cursor-pointer hover:bg-gray-100'} border-b
							{isSelected && (isOffline || isAway) ? 'bg-red-50' : isSelected ? 'bg-blue-100 text-blue-800' : ''}"
								on:click={() => !isCurrent && selectUser(user.id)}
							>
								<div class="relative">
									<div
										class="mr-3 h-8 w-8 {getAvatarColor(
											user
										)} flex items-center justify-center rounded-full"
									>
										<span class="text-white">{getUserInitials(user.name)}</span>
									</div>
									<!-- Status indicator dot -->
									<div
										class="{getStatusIndicator(
											user.status
										)} absolute bottom-0 right-2 h-2 w-2 rounded-full border border-white"
									></div>
								</div>
								<div class="flex-1">
									<div class="flex flex-wrap items-center gap-1">
										<span>{user.first_name} {user.last_name}</span>
										{#if isCurrent}
											<span class="ml-1">({t('current')})</span>
										{/if}
										{#if isSelected && isOffline}
											<span class="rounded-full bg-red-100 px-2 py-0.5 text-xs text-red-700"
												>{t('offline')}</span
											>
										{/if}
										{#if isSelected && isAway}
											<span class="rounded-full bg-yellow-100 px-2 py-0.5 text-xs text-yellow-700"
												>{t('away')}</span
											>
										{/if}
									</div>
									{#if user.roles}
										<div class="text-xs {isSelected && !isOffline && !isAway ? 'text-blue-800' : 'text-gray-500'}">{t('role')}: {t(user.roles.toLowerCase())}</div>
									{/if}
								</div>
							</div>
						{/each}
					{/if}
				</div>

				<input
					id="transfer-ticket-owner-new-owner-id"
					type="hidden"
					name="new_owner_id"
					value={selectedUserId}
				/>
			</div>
		</form>
	{/if}

	<!-- Warning message for offline or away users -->
	{#if selectedUserId}
		{@const selectedUser = users.find((user) => user.id.toString() === selectedUserId.toString())}
		{#if selectedUser && selectedUser.status && selectedUser.status.toLowerCase() === 'offline'}
			<Alert id="transfer-ticket-owner-offline-warning" color="red" class="mb-4">
				<span class="font-medium">{t('warning')}</span>: {t('assign_offline_warning')}
			</Alert>
		{:else if selectedUser && selectedUser.status && selectedUser.status.toLowerCase() === 'away'}
			<Alert id="transfer-ticket-owner-away-warning" color="yellow" class="mb-4">
				<span class="font-medium">{t('warning')}</span>: {t('assign_away_warning')}
			</Alert>
		{/if}
	{/if}

	<!-- Confirm and Cancel Button -->
	<svelte:fragment slot="footer">
		<div class="flex items-center gap-2">
			<Button
				id="transfer-ticket-owner-confirm-button"
				color="green"
				on:click={() => ticketTransferOwnerForm.requestSubmit()}
				disabled={!selectedUserId || isCurrentOwner(selectedUserId)}
			>
				<CheckOutline class="mr-2 h-4 w-4" />
				{t('save')}
			</Button>
			<Button
				id="transfer-ticket-owner-cancel-button"
				color="light"
				on:click={() => (ticketTransferOwnerModalOpen = false)}
			>
				{t('cancel')}
			</Button>
		</div>
	</svelte:fragment>
</Modal>
