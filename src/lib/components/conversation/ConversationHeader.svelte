<script lang="ts">
	import { UserHeadsetOutline } from 'flowbite-svelte-icons';
	import TransferTicketOwner from '$lib/components/UI/TransferTicketOwner.svelte';
	import ChangeTicketStatus from '$lib/components/UI/ChangeTicketStatus.svelte';
	import ChangeTicketPriority from '$lib/components/UI/ChangeTicketPriority.svelte';
	import { getPriorityClass, getStatusClass } from '$lib/utils';
	import { triggerRefresh } from '$lib/stores/refreshStore';
	import { PollingService } from '$lib/services/pollingService';
	import type { PollingConfig } from '$lib/types/polling';
	import { onMount, onDestroy } from 'svelte';

	export let customerId: number;
	export let customerName: string;
	export let channelName: string;
	export let connected: boolean | null = null;
	export let platformId: any = [];
	export let ticketId: number | null = null;
	export let access_token: string;

	export let users: any[];
	export let priorities: any[];
	export let statuses: any[];
	export let topics: any[];

	import { t } from '$lib/stores/i18n';
	import { services } from '$src/lib/api/features';

	// State variables
	let ticket: any = null;
	let loginUser: any = null;
	let loading = true;
	let error: string | null = null;
	let currentPriorityName: string = ''; // Add this reactive variable
	// Add a new state variable to track connection initialization
	let connectionInitialized = false;
	let connectionTimeout: ReturnType<typeof setTimeout> | null = null;
	let backendTicketId: string | null = null;



	// Polling service variables
	let pollingService: PollingService | null = null;
	let isPollingEnabled = false;

	// Async function to get ticket
	async function getTicket(customerId: number, platformId: any, accessToken: string) {
		try {
			loading = true;
			error = null;

			// If ticketId is provided, use it directly
			if (ticketId) {
				backendTicketId = ticketId.toString();
			} else {
				// Otherwise, get it from platform info
				const platformInfo = await services.customers.getPlatformInfo(
					customerId,
					platformId,
					accessToken
				);
				backendTicketId = platformInfo.id.toString();
			}

			// Get ticket information using the backendTicketId
			const response_ticket = await services.tickets.getById(backendTicketId, accessToken);
			const response_selfUserInfo = await services.users.getUserInfo(accessToken);

			ticket = response_ticket.tickets;
			loginUser = response_selfUserInfo.users;

			// Update the reactive variable
			currentPriorityName = ticket?.priority?.name || '';

			// For Debugging
			// console.log('ConversationHeader.svelte: getTicket(): Updated Ticket Information:', ticket);
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to fetch ticket';
			console.error('Error fetching ticket:', err);
			ticket = null;
			loginUser = null;
			currentPriorityName = '';
		} finally {
			loading = false;
		}
	}

	/**
	 * Custom fetcher function for polling service
	 * Fetches conversation header data (ticket and user info) for periodic updates
	 */
	async function fetchConversationHeaderData(): Promise<{ ticket: any; loginUser: any }> {
		try {
			// Determine the backend ticket ID
			let currentBackendTicketId = backendTicketId;

			if (ticketId) {
				currentBackendTicketId = ticketId.toString();
			} else if (customerId && platformId && access_token) {
				const platformInfo = await services.customers.getPlatformInfo(
					customerId,
					platformId,
					access_token
				);
				currentBackendTicketId = platformInfo.id.toString();
			}

			if (!currentBackendTicketId) {
				throw new Error('No ticket ID available for fetching data');
			}

			// Fetch ticket and user data in parallel
			const [response_ticket, response_selfUserInfo] = await Promise.all([
				services.tickets.getById(currentBackendTicketId, access_token),
				services.users.getUserInfo(access_token)
			]);

			return {
				ticket: response_ticket.tickets,
				loginUser: response_selfUserInfo.users
			};
		} catch (err) {
			console.error('ConversationHeader.svelte: fetchConversationHeaderData(): Error:', err);
			throw err;
		}
	}

	/**
	 * Handle polling data updates
	 */
	function handlePollingDataUpdate(data: { ticket: any; loginUser: any }): void {
		try {
			ticket = data.ticket;
			loginUser = data.loginUser;
			currentPriorityName = ticket?.priority?.name || '';

			// Clear any existing errors since we got fresh data
			error = null;

			// console.log('ConversationHeader.svelte: Polling data updated successfully');
		} catch (err) {
			console.error('ConversationHeader.svelte: Error handling polling data update:', err);
		}
	}

	/**
	 * Handle polling errors
	 */
	function handlePollingError(pollingError: Error): void {
		console.error('ConversationHeader.svelte: Polling error:', pollingError);
		// Don't update the error state for polling errors to avoid disrupting the UI
		// The component will continue to show the last successful data
	}

	/**
	 * Initialize polling service for conversation header data
	 */
	function initializePolling(): void {
		try {
			pollingService = PollingService.getInstance();

			const pollingConfig: PollingConfig = {
				interval: 3000, // 3 seconds - matches other components
				customFetcher: fetchConversationHeaderData,
				onDataChange: handlePollingDataUpdate,
				onError: handlePollingError,
				debugMode: false // Set to true for debugging
			};

			const success = pollingService.registerEndpoint('conversation-header', pollingConfig);

			if (success) {
				isPollingEnabled = true;
				console.log('ConversationHeader.svelte: Polling started successfully');
			} else {
				console.error('ConversationHeader.svelte: Failed to start polling');
			}
		} catch (err) {
			console.error('ConversationHeader.svelte: Error initializing polling:', err);
		}
	}

	/**
	 * Cleanup polling service
	 */
	function cleanupPolling(): void {
		try {
			if (pollingService && isPollingEnabled) {
				pollingService.unregisterEndpoint('conversation-header');
				isPollingEnabled = false;
				console.log('ConversationHeader.svelte: Polling cleanup completed');
			}
		} catch (err) {
			console.error('ConversationHeader.svelte: Error during polling cleanup:', err);
		}
	}

	// Enhanced badge configuration functions
	function getStatusBadgeConfig(id: number, status: string) {
		const configs: Record<string, { class: string; text: string; showIcon: boolean }> = {
			none: {
				class: '',
				text: '',
				showIcon: false
			},
			closed: {
				class: getStatusClass(id),
				text: t('tickets_closed'),
				showIcon: true
			},
			open: {
				class: getStatusClass(id),
				text: t('tickets_open'),
				showIcon: false
			},
			assigned: {
				class: getStatusClass(id),
				text: t('tickets_assigned'),
				showIcon: false
			},
			waiting: {
				class: getStatusClass(id),
				text: t('tickets_waiting'),
				showIcon: false
			},
			pending_to_close: {
				class: getStatusClass(id),
				text: t('tickets_pending_to_close'),
				showIcon: false
			}
		};
		return configs[status?.toLowerCase()] || configs['none'];
	}

	function getPriorityBadgeConfig(priorityName: string) {
		const configs: Record<string, { class: string; text: string; showIcon: boolean }> = {
			none: {
				class: '',
				text: '',
				showIcon: false
			},
			Low: {
				class: getPriorityClass(priorityName),
				text: t('tickets_priority_low'),
				showIcon: false
			},
			Medium: {
				class: getPriorityClass(priorityName),
				text: t('tickets_priority_medium'),
				showIcon: true
			},
			High: {
				class: getPriorityClass(priorityName),
				text: t('tickets_priority_high'),
				showIcon: true
			},
			Immediately: {
				class: getPriorityClass(priorityName),
				text: t('tickets_priority_immediately'),
				showIcon: true
			}
		};
		return configs[priorityName] || configs['none'];
	}

	// Lifecycle: Initialize data and polling when component mounts
	onMount(async () => {
		// Perform immediate initial data load if dependencies are available
		if (customerId && (platformId || ticketId) && access_token) {
			await getTicket(customerId, platformId, access_token);

			// Start polling after initial data load
			initializePolling();
		}
	});

	// Lifecycle: Cleanup polling when component is destroyed
	onDestroy(() => {
		cleanupPolling();

		// Clear connection timeout if it exists
		if (connectionTimeout) {
			clearTimeout(connectionTimeout);
		}
	});

	// Reactive function to refetch when dependencies change (for cases where they change after mount)
	$: if (customerId && (platformId || ticketId) && access_token) {
		// Only call getTicket if we haven't initialized polling yet
		// This prevents duplicate calls during initial mount
		if (!isPollingEnabled) {
			getTicket(customerId, platformId, access_token).then(() => {
				// Start polling after the reactive data load
				if (!isPollingEnabled) {
					initializePolling();
				}
			});
		}
	}

	// Modal opening functions
	function openTransferOwnerModal() {
		if (ticket && backendTicketId) {
			// Call the modal's opening function directly
			transferOwnerComponent?.openTicketTransferOwnerModal({ ...ticket, id: backendTicketId });
		}
	}

	function openChangeStatusModal() {
		if (ticket && backendTicketId) {
			// Call the modal's opening function directly
			changeStatusComponent?.openChangeTicketStatusModal({ ...ticket, id: backendTicketId });
		}
	}

	function openChangePriorityModal() {
		if (ticket && backendTicketId) {
			// Call the modal's opening function directly
			changePriorityComponent?.openTicketTransferOwnerModal({ ...ticket, id: backendTicketId });
		}
	}

	// Component references
	let transferOwnerComponent: any;
	let changeStatusComponent: any;
	let changePriorityComponent: any;

	// Function to refresh ticket data after operations
	async function refreshTicketData() {
		if (customerId && (platformId || ticketId) && access_token) {
			// Perform immediate refresh for user feedback
			await getTicket(customerId, platformId, access_token);

			// Trigger global refresh to update platform identity list
			triggerRefresh();
		}
	}

	// Reactive badge configurations
	$: statusBadgeConfig = getStatusBadgeConfig(ticket?.status_id, ticket?.status);
	$: priorityBadgeConfig = getPriorityBadgeConfig(currentPriorityName);
	// Modify the reactive statement to track when connection status is first determined
	$: if (connected !== null) {
		connectionInitialized = false;
		// Clear any existing timeout
		if (connectionTimeout) {
			clearTimeout(connectionTimeout);
		}

		// Set a minimum display time for the connecting state (500ms)
		connectionTimeout = setTimeout(() => {
			connectionInitialized = true;
		}, 500);
	}
</script>

<div
	id="conv-header-conversation-header"
	class="min-h-[120px] border-b border-gray-200 bg-white px-4 py-4 sm:px-6"
>
	<div class="flex flex-col space-y-4 lg:flex-row lg:items-start lg:justify-between lg:space-y-0">
		<!-- Left: Avatar + Customer Info + Status Badges -->
		<div class="flex flex-col space-y-3">
			<!-- Avatar + Customer Info Row -->
			<div class="flex items-center space-x-3">
				<!-- Avatar -->
				<!-- <div class="flex-shrink-0">
					<div
						class="flex h-10 w-10 items-center justify-center rounded-full bg-blue-500 text-sm font-semibold text-white"
						role="img"
						aria-label="Customer avatar for {customerName}"
					>
						{getInitials(customerName)}
					</div>
				</div> -->
				<!-- Customer Info -->
				<div class="flex min-w-0 flex-col">
					<h2 id="conv-header-customer-name" class="truncate text-lg font-semibold text-gray-900">
						{customerName}
					</h2>
					<p id="conv-header-channel-info" class="truncate text-sm text-gray-500">
						{t('chat_center_filter_channel')}: {channelName}
					</p>
				</div>
			</div>
		</div>

		<!-- Right: Timestamp + Actions -->
		<div
			class="flex flex-col items-start justify-end space-y-2 sm:flex-row sm:items-center sm:space-x-4 sm:space-y-0"
		>
			<!-- Last Activity -->
			<!-- {#if ticket?.updated_at}
				<div class="flex items-center text-sm text-gray-500">
					<ClockSolid class="mr-1 h-4 w-4" aria-hidden="true" />
					<span class="whitespace-nowrap"
						>Last activity: {timeAgo(ticket.updated_at, ticket?.status || '')}</span
					>
				</div>
			{/if} -->

			<!-- Actions - Removed dropdown menu -->
			<div class="flex items-center justify-end gap-2">
				<!-- Actions will be handled by clickable badges -->
			</div>
		</div>
	</div>

	<!-- Status Badges Row -->
	<div class="mt-3 flex w-full items-center justify-between">
		<!-- Left side badges group -->
		<div class="flex flex-wrap items-center gap-2">
			{#if !loading && ticket}
				<!-- Status Badge - Clickable -->
				<button
					id="conv-header-badge-status"
					class="flex items-center space-x-1 rounded px-2 py-1 {statusBadgeConfig.class} cursor-pointer transition-all duration-200 hover:opacity-80 hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
					aria-label="Click to change ticket status: {statusBadgeConfig.text}"
					on:click={openChangeStatusModal}
					on:keydown={(e) => e.key === 'Enter' && openChangeStatusModal()}
					tabindex="0"
				>
					<span class="whitespace-nowrap text-xs font-medium">
						{#if statusBadgeConfig.text !== ''}
							{t('table_status')}: {statusBadgeConfig.text}
						{/if}
					</span>
				</button>

				<!-- Priority Badge - Clickable -->
				<button
					id="conv-header-badge-priority"
					class="flex items-center space-x-1 rounded px-2 py-1 {priorityBadgeConfig.class} cursor-pointer transition-all duration-200 hover:opacity-80 hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
					aria-label="Click to change ticket priority: {priorityBadgeConfig.text}"
					on:click={openChangePriorityModal}
					on:keydown={(e) => e.key === 'Enter' && openChangePriorityModal()}
					tabindex="0"
				>
					<span class="whitespace-nowrap text-xs font-medium">
						{#if priorityBadgeConfig.text !== ''}
							{t('table_priority')}: {priorityBadgeConfig.text}
						{/if}
					</span>
				</button>
			{:else if loading}
				<!-- Loading state for badges -->
				<div
					id="conv-header-badge-loading"
					class="flex items-center space-x-1 rounded bg-gray-100 px-2 py-1"
				>
					<span class="whitespace-nowrap text-xs font-medium text-gray-500">
						{t('loading')}...
					</span>
				</div>
			{/if}
			<!-- For Debugging -->
			<!-- <Badge color="purple">
				Customer ID: {customerId} | Customer Name: {customerName} | Ticket ID: {backendTicketId}
			</Badge> -->
		</div>

		<div class="flex gap-2">
			<!-- Owner Badge - Clickable -->
			{#if !loading && ticket}
				<button
					id="conv-header-badge-owner"
					class="flex items-center space-x-1 rounded border border-gray-600 px-2 py-1 cursor-pointer transition-all duration-200 hover:bg-gray-50 hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
					aria-label="Click to transfer ticket owner: {ticket.owner?.name}"
					on:click={openTransferOwnerModal}
					on:keydown={(e) => e.key === 'Enter' && openTransferOwnerModal()}
					tabindex="0"
				>
					<UserHeadsetOutline class="mr-1 h-4 w-4 text-gray-600" aria-hidden="true" />
					<span class="whitespace-nowrap text-xs font-medium">
						{ticket.owner?.name}
					</span>
				</button>
			{/if}
			<!-- Connection Badge (right-aligned) -->
			<div
				id="conv-header-badge-connection"
				class="flex items-center space-x-1 rounded border px-2 py-1 {!connectionInitialized
					? 'border-gray-500 text-gray-500'
					: connected === true
						? 'border-green-500 text-green-700'
						: 'border-red-500 text-red-700'}"
				role="status"
				aria-label="Connection status: {!connectionInitialized
					? 'initializing'
					: connected
						? t('connected')
						: t('disconnected')}"
			>
				{#if !connectionInitialized}
					<div class="h-2 w-2 animate-pulse rounded-full bg-gray-400" aria-hidden="true"></div>
					<span class="whitespace-nowrap text-xs font-medium">
						{t('connecting')}
					</span>
				{:else}
					<div
						class="h-2 w-2 animate-pulse rounded-full {connected ? 'bg-green-500' : 'bg-red-500'}"
						aria-hidden="true"
					></div>
					<span class="whitespace-nowrap text-xs font-medium">
						{connected ? t('connected') : t('disconnected')}
					</span>
				{/if}
			</div>
		</div>
	</div>
</div>

<!-- Modal Components -->
{#if ticket && backendTicketId}
	<!-- Transfer Ticket Owner Modal -->
	<TransferTicketOwner
		bind:this={transferOwnerComponent}
		ticket={{ ...ticket, id: backendTicketId }}
		{users}
		loggedInUsername={loginUser?.username}
		loggedInRole={loginUser?.roles?.[0]?.name}
		isDropDownItem={false}
		showButton={false}
		onSuccess={refreshTicketData}
	/>

	<!-- Change Ticket Status Modal -->
	<ChangeTicketStatus
		bind:this={changeStatusComponent}
		ticket={{ ...ticket, id: backendTicketId }}
		{statuses}
		ticket_topics={topics}
		isDropDownItem={false}
		showButton={false}
		onSuccess={refreshTicketData}
	/>

	<!-- Change Ticket Priority Modal -->
	<ChangeTicketPriority
		bind:this={changePriorityComponent}
		ticket={{ ...ticket, id: backendTicketId }}
		{priorities}
		isDropDownItem={false}
		showButton={false}
		onSuccess={refreshTicketData}
	/>
{/if}
